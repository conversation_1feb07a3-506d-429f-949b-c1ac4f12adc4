/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://pedrolaracuente.com', // Replace with your actual domain
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: ['/404', '/500', '/server-sitemap.xml'],
  robotsTxtOptions: {
    additionalSitemaps: [
      'https://pedrolaracuente.com/server-sitemap.xml', // Replace with your actual domain
    ],
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/404', '/500'],
      },
    ],
  },
  transform: async (config, path) => {
    // Custom transform function for dynamic routes
    return {
      loc: path, // => this will be exported as http(s)://<config.siteUrl>/<path>
      changefreq: path === '/' ? 'daily' : 'weekly',
      priority: path === '/' ? 1.0 : 0.8,
      lastmod: new Date().toISOString(),
    }
  },
}
