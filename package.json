{"name": "temp-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "gray-matter": "^4.0.3", "next": "15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "refractor": "^4.8.1", "rehype-prism-plus": "^2.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-html": "^16.0.1", "remark-rehype": "^11.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "next-sitemap": "^4.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}