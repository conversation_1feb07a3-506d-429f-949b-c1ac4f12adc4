'use client';

import { MoonIcon, SunIcon } from '@heroicons/react/24/outline';
import { useTheme } from '@/app/providers/theme-provider';
type Theme = 'light' | 'dark' | 'system';

const SystemIcon = () => (
  <div className="relative w-5 h-5">
    <SunIcon className="absolute w-4 h-4 text-gray-600 dark:text-gray-300 left-0 top-0" />
    <MoonIcon className="absolute w-3 h-3 text-gray-600 dark:text-gray-300 right-0 bottom-0" />
  </div>
);

export default function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    const themeOrder: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themeOrder.indexOf(theme || 'system');
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    setTheme(themeOrder[nextIndex]);
  };

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <SunIcon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
      ) : theme === 'dark' ? (
        <MoonIcon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
      ) : (
        <SystemIcon />
      )}
    </button>
  );
}