import { FaEnvelope, FaGithub, FaLinkedin } from 'react-icons/fa';

export default function Contact() {
  return (
    <main className="min-h-screen p-8 pt-32">
      <section className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Get in Touch</h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
            I'm always interested in hearing about new projects and opportunities.
            Feel free to reach out through any of the following channels:
          </p>
          
          <div className="space-y-6">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center gap-4 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              <FaEnvelope className="text-2xl" />
              <span><EMAIL></span>
            </a>
            
            <a
              href="https://github.com/laracuentepedro"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-4 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              <FaGithub className="text-2xl" />
              <span>GitHub Profile</span>
            </a>
            
            <a
              href="https://www.linkedin.com/in/pedro-laracuente-878b3339"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-4 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              <FaLinkedin className="text-2xl" />
              <span>Connect with me on LinkedIn</span>
            </a>
          </div>
        </div>
      </section>
    </main>
  );
}