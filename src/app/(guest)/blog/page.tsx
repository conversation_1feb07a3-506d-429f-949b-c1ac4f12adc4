import Link from 'next/link';
import { getAllPosts } from '@/lib/markdown';
import { Metadata } from 'next';

export const revalidate = 3600; // Revalidate every hour

export const metadata: Metadata = {
  title: 'Blog | Your Portfolio',
  description: 'Technical articles and insights',
};

export default async function BlogPage() {
  const posts = await getAllPosts();

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <main className="max-w-4xl mx-auto px-4 py-8 pt-32">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Blog</h1>
        <div className="space-y-8">
          {posts.map((post) => (
            <article key={post.id} className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                <Link href={`/blog/${post.id}`} className="hover:text-blue-600 dark:hover:text-blue-400">
                  {post.title}
                </Link>
              </h2>
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <time dateTime={post.date}>{post.date}</time>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{post.excerpt}</p>
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-sm text-gray-700 dark:text-gray-300 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </article>
          ))}
        </div>
      </main>
    </div>
  );
}