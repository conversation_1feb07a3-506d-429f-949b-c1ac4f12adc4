import { notFound } from 'next/navigation';
import { getPostData } from '@/lib/markdown';
import { Metadata } from 'next';

export const revalidate = 3600; // Revalidate every hour

type Props = {
  params: { id: string }
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const post = await getPostData(params.id).catch(() => null);
  
  if (!post) {
    return {
      title: 'Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: `${post.title} | <PERSON><PERSON>`,
    description: post.excerpt,
  };
}

export default async function BlogPostPage({ params }: Props) {
  const post = await getPostData(params.id).catch(() => null);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <main className="max-w-4xl mx-auto px-4 py-8 pt-32">
        <article className="prose prose-lg dark:prose-invert max-w-none prose-p:leading-relaxed prose-p:mb-8 prose-headings:mb-8 prose-ul:my-8 prose-li:mb-4 prose-pre:mb-8">
          <header className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">{post.title}</h1>
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
              <time dateTime={post.date}>{new Date(post.date).toLocaleDateString()}</time>
              <span>•</span>
              <span>{post.author}</span>
            </div>
            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-4">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-sm text-gray-700 dark:text-gray-300 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </header>
          {post.coverImage && (
            <img
              src={post.coverImage}
              alt={post.title}
              className="w-full h-64 object-cover rounded-lg mb-8"
            />
          )}
          <div 
            className="markdown-content" 
            dangerouslySetInnerHTML={{ __html: post.contentHtml }} 
          />
        </article>
      </main>
    </div>
  );
}
