import Image from 'next/image';
import Link from 'next/link';
import { projectsList } from '@/lib/data/projects';

// Select the first 3 projects to display as featured works
const featuredWorks = projectsList.slice(0, 3).map(project => ({
  id: project.id,
  title: project.title,
  year: project.year,
  category: project.technologies[0], // Using the first technology as category
  description: project.description,
  image: project.thumbnail
}));


export default function FeaturedWorks() {
  return (
    <section className="py-16">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold mb-12 text-gray-900 dark:text-white">Side Projects</h2>
        <div className="space-y-12">
          {featuredWorks.map((work) => (
            <article 
              key={work.id}
              className="flex flex-col md:flex-row gap-8 border-b border-gray-200 dark:border-gray-800 pb-8"
            >
              <div className="md:w-1/3 aspect-[4/3] relative">
                <Image
                  src={work.image}
                  alt={work.title}
                  fill
                  className="object-cover rounded-lg"
                />
              </div>
              <div className="md:w-2/3">
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                  <Link 
                    href={`/projects/${work.id}`}
                    className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    {work.title}
                  </Link>
                </h3>
                <div className="flex gap-4 items-center mb-4">
                  <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full">
                    {work.year}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {work.category}
                  </span>
                </div>
                <p className="text-gray-600 dark:text-gray-400">{work.description}</p>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
}