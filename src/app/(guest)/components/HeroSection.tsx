import Image from 'next/image';
import Link from 'next/link';
import { SOCIAL_LINKS, SITE_CONFIG } from '@/constants';

export default function HeroSection() {
  return (
    <section className="flex flex-col-reverse md:flex-row items-center justify-between gap-8 py-16 bg-white dark:bg-gray-900">
      <div className="flex-1 text-center md:text-left">
        <h1 className="text-5xl font-bold mb-4 text-gray-900 dark:text-white">
          Hi, I'm <PERSON>
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
          Software Developer & Electrical Engineer
        </p>
        <p className="text-lg text-gray-500 dark:text-gray-400 mb-8 max-w-2xl">
          I build modern web applications with a focus on user experience.
          Let's create something amazing together!
        </p>
        {/* <div className="flex gap-4 justify-center md:justify-start">
          <Link
            href="/portfolio"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all hover:scale-105"
          >
            View Portfolio
          </Link>
          <Link
            href="/contact"
            className="px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all hover:scale-105"
          >
            Contact Me
          </Link>
        </div> */}
        <div className="flex gap-6 mt-8 justify-center md:justify-start">
          {SOCIAL_LINKS.map((link) => (
            <a
              key={link.platform}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors group"
            >
              <Image
                src={link.icon}
                alt={link.platform}
                width={24}
                height={24}
                className="hover:scale-110 transition-transform opacity-75 dark:invert dark:opacity-80 group-hover:opacity-100 dark:group-hover:opacity-100"
              />
            </a>
          ))}
        </div>
      </div>
      <div className="relative w-48 h-48 md:w-64 md:h-64 rounded-full overflow-hidden ring-4 ring-blue-600/20 hover:ring-blue-600/40 transition-all hover:scale-105">
        <Image
          src="/images/IMG_8992-2.jpg"
          alt="Your Name"
          fill
          className="object-cover"
          priority
        />
      </div>
    </section>
  );
}