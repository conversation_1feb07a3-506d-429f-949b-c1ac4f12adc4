import Image from 'next/image';
import Link from 'next/link';

export default function Hero() {
  return (
    <section className="relative min-h-screen bg-white dark:bg-gray-900 flex items-center">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        <div>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Hi, I'm <PERSON>rae
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            A passionate web developer focused on creating beautiful and functional applications
            that solve real-world problems.
          </p>
          <div className="flex gap-4">
            <Link
              href="/contact"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Get in touch
            </Link>
            <Link
              href="/projects"
              className="border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              View my work
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}