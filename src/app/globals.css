@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .markdown-content {
    @apply prose prose-lg dark:prose-invert max-w-none;
    @apply prose-headings:text-gray-900 dark:prose-headings:text-white;
    @apply prose-p:text-gray-700 dark:prose-p:text-gray-300;
    @apply prose-a:text-blue-600 dark:prose-a:text-blue-400;
    @apply prose-code:bg-gray-100 dark:prose-code:bg-gray-800;
    @apply prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800;
    @apply prose-pre:p-4 prose-pre:rounded-lg;
    @apply prose-code:px-1 prose-code:py-0.5 prose-code:rounded;
    @apply prose-strong:text-gray-900 dark:prose-strong:text-white;
    @apply prose-ul:list-disc prose-ul:pl-4;
    @apply prose-ol:list-decimal prose-ol:pl-4;
    @apply prose-li:my-2;
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}
