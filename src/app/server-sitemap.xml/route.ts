import { getServerSideSitemap } from 'next-sitemap';
import { BLOG_POSTS, SITE_CONFIG } from '@/constants';

export async function GET() {
  const posts = BLOG_POSTS.map((post) => ({
    loc: `${SITE_CONFIG.url}/blog/${post.slug}`,
    lastmod: new Date().toISOString(),
    changefreq: 'weekly',
    priority: 0.7,
  }));

  return getServerSideSitemap([
    {
      loc: SITE_CONFIG.url,
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 1.0,
    },
    ...posts,
  ]);
}
