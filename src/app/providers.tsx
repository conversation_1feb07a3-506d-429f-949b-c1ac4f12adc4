"use client";

import { createContext, useContext, useEffect, useState } from "react";

type Theme = "light" | "dark" | "system";

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>("system");

  useEffect(() => {
    const handleSystemThemeChange = (e: MediaQueryListEvent | MediaQueryList) => {
      if (theme === "system") {
        document.documentElement.classList.toggle("dark", e.matches);
      }
    };

    const systemDarkTheme = window.matchMedia("(prefers-color-scheme: dark)");

    // Initial theme setup
    if (theme === "system") {
      handleSystemThemeChange(systemDarkTheme);
    } else {
      document.documentElement.classList.toggle("dark", theme === "dark");
    }

    // Listen for system theme changes
    systemDarkTheme.addEventListener("change", handleSystemThemeChange);

    return () => {
      systemDarkTheme.removeEventListener("change", handleSystemThemeChange);
    };
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}