import { Metadata } from 'next';
import { SITE_CONFIG } from '@/constants';

interface GenerateMetadataProps {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  authors?: string[];
  keywords?: string[];
}

export const generateMetadata = ({
  title,
  description,
  image,
  noIndex = false,
  type = 'website',
  publishedTime,
  authors,
  keywords,
}: GenerateMetadataProps): Metadata => {
  const finalTitle = title 
    ? `${title} | ${SITE_CONFIG.title}`
    : SITE_CONFIG.title;
  
  const finalDescription = description || SITE_CONFIG.description;
  const finalImage = image || '/og-image.png';
  const canonicalUrl = new URL(title ? `/${title.toLowerCase().replace(/ /g, '-')}` : '/', SITE_CONFIG.url).toString();

  return {
    title: finalTitle,
    description: finalDescription,
    keywords: keywords,
    authors: authors?.map(author => ({ name: author })),
    metadataBase: new URL(SITE_CONFIG.url),
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: finalTitle,
      description: finalDescription,
      url: canonicalUrl,
      siteName: SITE_CONFIG.title,
      images: [
        {
          url: finalImage,
          width: 1200,
          height: 630,
          alt: finalTitle,
        },
      ],
      type,
      ...(publishedTime && {
        publishedTime,
        authors,
      }),
    },
    twitter: {
      card: 'summary_large_image',
      title: finalTitle,
      description: finalDescription,
      images: [finalImage],
      creator: '@yourtwitterhandle', // Add your Twitter handle
    },
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  };
};
