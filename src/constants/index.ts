import { NavItem, SocialLink } from '@/types';

export const SITE_CONFIG = {
  title: '<PERSON>',
  description: 'Software Development',
  url: 'https://pedrodev.com', // Replace with your actual domain
  author: '<PERSON>',
};

export const NAVIGATION_ITEMS: NavItem[] = [
  { label: 'Home', href: '/' },
  { label: 'Blog', href: '/blog' },
  { label: 'Projects', href: '/projects' },
  { label: 'Contact', href: '/contact' },
];

export const SOCIAL_LINKS: SocialLink[] = [
  {
    platform: 'GitHub',
    url: 'https://github.com/laracuentepedro',
    icon: '/icons/github.svg',
  },
  {
    platform: 'LinkedIn',
    url: 'https://www.linkedin.com/in/pedro-laracuente-878b3339',
    icon: '/icons/linkedin.svg',
  },
  {
    platform: 'Twitter',
    url: '#',
    icon: '/icons/twitter.svg',
  },
];

export const SKILLS_CATEGORIES = {
  languages: ['TypeScript', 'JavaScript', 'Python'],
  frameworks: ['React', 'Next.js', 'Node.js'],
  tools: ['Git', 'Docker', 'AWS'],
};

export const BLOG_POSTS: BlogPost[] = [
  {
    slug: 'session-state-deep-dive',
    title: 'Deep Dive: Handling Session State in React Applications',
    date: '2024-02-15',
    excerpt: "Explore how Rica's Plants implements robust session management using React's Context API and browser local storage for secure, persistent user authentication.",
    content: `In modern web applications, managing session state efficiently is critical for ensuring a secure and consistent user experience...`,
    tags: ['React', 'Authentication', 'TypeScript', 'Context API']
  },
  // Add more blog posts here
];
