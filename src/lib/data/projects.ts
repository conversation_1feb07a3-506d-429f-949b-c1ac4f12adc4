import { StaticImageData } from 'next/image';

export interface Project {
  id: string;
  title: string;
  description: string;
  thumbnail: string | StaticImageData;
  technologies: string[];
  longDescription?: string;
  liveUrl?: string;
  githubUrl?: string;
  year: number
}

export const projects: { [key: string]: Project } = {
  "1": {
    id: "1",
    title: "Rica's Plants",
    description: "A modern e-commerce web app for plant enthusiasts built with React.",
    thumbnail: "/images/projects/ai-image.jpg",
    technologies: ["React", "Vite", "Tailwind CSS", "React Router", "JWT", "Context API"],
    longDescription: "A comprehensive e-commerce platform for plant enthusiasts featuring user authentication, shopping cart functionality, and session management. Built with React 18 and Vite for optimal performance, the application provides a seamless shopping experience with features like dynamic plant listings, cart management, and a modern UI styled with Tailwind CSS.",
    liveUrl: "https://ricahs-plants.pedrolaracuente.com",
    githubUrl: "https://github.com/laracuentepedro/ricahs-plants",
    year: 2024
  },
  "2": {
    id: "2",
    title: "AI OCR Processor",
    description: "A React.js web app that leverages Mistral's OCR AI to extract text and images from documents and images and present them in markdown format.",
    thumbnail: "/images/projects/ai-ocr.png",
    technologies: ["React", "TypeScript", "Mistral AI", "TailwindCSS"],
    longDescription: "A React.js web app that leverages Mistral's OCR AI to extract text and images from documents and images and present them in markdown format.",
    liveUrl: "https://ocr.pedrolaracuente.com/",
    githubUrl: "https://github.com/laracuentepedro/Mistral-OCR",
    year: 2025
  },
  "3": {
    id: "3",
    title: "Data Structures and Algorithms Playground",
    description: "An educational app to help you learn andvisualize data structures and algorithms.",
    thumbnail: "/images/projects/dsa.png",
    technologies: ["React", "TypeScript", "TailwindCSS"],
    longDescription: "An educational app to help you learn andvisualize data structures and algorithms.",
    liveUrl: "https://dsa.pedrolaracuente.com/",
    githubUrl: "https://dsa.pedrolaracuente.com/",
    year: 2025
  },

};

export const projectsList = Object.values(projects);