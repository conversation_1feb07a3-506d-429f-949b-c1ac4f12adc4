import { Project } from "../projects";

export const ecommerceProject: Project = {
  id: "lp-ecommerce",
  title: "LP Ecommerce",
  description: "A modern e-commerce platform built with Next.js App Router, TypeScript, Prisma, and Tailwind CSS. This project demonstrates a simple storefront that allows users to view products, and serves as a template to eventually introduce more advanced e-commerce features.",
  thumbnail: "/images/projects/ecommerce/main.png",
  technologies: [
    "Next.js 15+",
    "React 19+",
    "TypeScript 5+",
    "Prisma 6+",
    "Neon PostgreSQL",
    "Tailwind CSS 3+"
  ],
  longDescription: "A full-featured e-commerce platform utilizing Next.js App Router with folder-based routing, providing type-safe environment with TypeScript, and Prisma ORM with Neon PostgreSQL for scalable database management. Features include responsive UI with Tailwind CSS, dynamic product management system, light/dark mode theming, and custom error handling with loading states. Current functionalities include homepage with newest products listing, detailed product pages, theme toggling, and navigation header with cart and sign-in placeholders. Planned features include user authentication, shopping cart functionality, payment integration (Stripe & PayPal), order tracking, user profiles, admin dashboard, email receipts, and product reviews.",
  liveUrl: "https://lp-ecommerce.pedrolaracuente.com/",
  githubUrl: "https://github.com/laracuentepedro/lp-ecommerce"
};