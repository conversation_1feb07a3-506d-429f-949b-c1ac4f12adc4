import { readFileSync, readdirSync } from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import remarkRehype from 'remark-rehype';
import rehypePrism from 'rehype-prism-plus';
import rehypeStringify from 'rehype-stringify';

// Import languages for syntax highlighting
import bash from 'refractor/lang/bash';
import javascript from 'refractor/lang/javascript';
import typescript from 'refractor/lang/typescript';
import jsx from 'refractor/lang/jsx';
import tsx from 'refractor/lang/tsx';
import css from 'refractor/lang/css';
import json from 'refractor/lang/json';
import { refractor } from 'refractor';

// Register languages
refractor.register(bash);
refractor.register(javascript);
refractor.register(typescript);
refractor.register(jsx);
refractor.register(tsx);
refractor.register(css);
refractor.register(json);
import { cache } from 'react';

export interface Post {
  id: string;
  title: string;
  date: string;
  author: string;
  excerpt: string;
  tags?: string[];
  draft?: boolean;
  coverImage?: string;
  contentHtml: string;
}

const postsDirectory = path.join(process.cwd(), 'content/blog');

// Cache post data in development
export const getPostData = cache(async (id: string): Promise<Post> => {
  const fullPath = path.join(postsDirectory, `${id}.md`);
  const fileContents = readFileSync(fullPath, 'utf8');
  const { data, content } = matter(fileContents);
  
  const processedContent = await remark()
    .use(remarkRehype)
    .use(rehypePrism)
    .use(rehypeStringify)
    .process(content);
  const contentHtml = processedContent.toString();

  validatePostMetadata(data);

  return {
    id,
    contentHtml,
    title: data.title,
    date: data.date,
    author: data.author,
    excerpt: data.excerpt,
    tags: data.tags,
    draft: data.draft,
    coverImage: data.coverImage,
  };
});

// Get all posts with optional filtering
export async function getAllPosts(options?: { 
  includeDrafts?: boolean,
  tags?: string[] 
}): Promise<Post[]> {
  const fileNames = readdirSync(postsDirectory);
  const allPosts = await Promise.all(
    fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(async fileName => {
        const id = fileName.replace(/\.md$/, '');
        return getPostData(id);
      })
  );

  // Filter and sort posts
  return allPosts
    .filter(post => options?.includeDrafts || !post.draft)
    .filter(post => !options?.tags || post.tags?.some(tag => options.tags?.includes(tag)))
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

// Validate required metadata fields
function validatePostMetadata(metadata: any) {
  const requiredFields = ['title', 'date', 'author', 'excerpt'] as const;
  const missingFields = requiredFields.filter(field => !metadata[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required metadata fields: ${missingFields.join(', ')}`);
  }
}
